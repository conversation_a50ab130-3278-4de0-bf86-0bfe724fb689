# Model files
*.pt

# Logs and runtime files
logs/*
*.log
*.pid
error.log

# Database files
*.db
*.db-journal
*.db-wal
*.db-shm

# Documentation (if auto-generated)
# docs/*

# Images (except resource images)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.webp
!resource/images/*.jpg
!resource/images/*.jpeg
!resource/images/*.png

# Storage directories
image_storage/*
false_positive_packages/*.zip

# Python
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/
