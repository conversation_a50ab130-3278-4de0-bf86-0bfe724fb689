{"database": {"path": "yolo_inference.db", "schema_file": "database_schema.sql", "backup_enabled": true, "backup_interval_hours": 24}, "model": {"model_path": "best.pt", "confidence_threshold": 0.1, "warmup_enabled": true, "warmup_image_path": "resource/images/test_image.jpg"}, "storage": {"base_path": "image_storage", "max_file_size_mb": 16, "cleanup_enabled": true, "cleanup_days": 30}, "api": {"host": "0.0.0.0", "port": 5000, "debug": false, "max_content_length_mb": 16, "cors_enabled": true, "test_mode": false}, "logging": {"level": "INFO", "log_dir": "logs", "max_file_size_mb": 10, "backup_count": 10, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "statistics": {"enabled": true, "auto_refresh_seconds": 30, "max_timing_records": 100}, "false_positive": {"package_output_dir": "false_positive_packages", "auto_package_enabled": false, "package_interval_days": 7}}