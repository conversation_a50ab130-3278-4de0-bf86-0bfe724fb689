
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>算法在线推理服务 - 统计面板</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
                .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
                .stat-card { background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #007bff; }
                .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
                h1 { color: #333; text-align: center; }
                h2 { color: #007bff; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>📊 算法在线推理服务 - 统计面板</h1>

                <h2>📈 总体统计</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>API调用次数</h3>
                        <div class="stat-number">59</div>
                        <p>成功率: 86.44%</p>
                    </div>
                    <div class="stat-card">
                        <h3>误报统计</h3>
                        <div class="stat-number">86</div>
                        <p>误报率: 9.82%</p>
                    </div>
                    <div class="stat-card">
                        <h3>推理次数</h3>
                        <div class="stat-number">0</div>
                        <p>平均时间: 0 ms</p>
                    </div>
                </div>

                <h2>🔗 快速链接</h2>
                <div class="stat-card">
                    <p><a href="/health">🔍 健康检查</a></p>
                    <p><a href="/false-positive-viewer">🖼️ 误报浏览器</a></p>
                    <p><a href="/api/statistics">📊 API统计数据</a></p>
                </div>

                <p style="text-align: center; color: #666; margin-top: 30px;">
                    服务时间: 2025-08-05 19:43:22
                </p>
            </div>
        </body>
        </html>
        