#!/usr/bin/env python3
"""
统计服务模块
提供API调用统计、推理时间统计等功能
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..core.config import get_config
from ..utils.logging_config import get_logger


class StatisticsService:
    """统计服务类"""
    
    def __init__(self, db_manager=None):
        """
        初始化统计服务
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.db_manager = db_manager
        
        # 统计配置
        self.stats_config = {
            'predict': {
                'name': '图像推理',
                'method': 'POST',
                'description': '上传图像进行目标检测推理',
                'enabled': True
            },
            'health': {
                'name': '健康检查',
                'method': 'GET',
                'description': '检查服务状态和模型加载情况',
                'enabled': False
            }
        }
        
        # 内存中的统计数据（用于实时显示）
        self.api_stats = {}
        for endpoint, config in self.stats_config.items():
            if config['enabled']:
                self.api_stats[endpoint] = {'total': 0, 'success': 0, 'errors': 0}
        
        # 推理时间统计
        self.inference_timing_stats = {
            'total_inferences': 0,
            'total_time_ms': 0,
            'preprocess_times': [],
            'inference_times': [],
            'postprocess_times': [],
            'annotation_times': [],
            'total_times': [],
            'min_total_time': float('inf'),
            'max_total_time': 0,
            'avg_total_time': 0,
            'min_inference_time': float('inf'),
            'max_inference_time': 0,
            'avg_inference_time': 0
        }
    
    def update_api_stats(self, endpoint: str, success: bool = True):
        """
        更新API调用统计
        
        Args:
            endpoint: API端点名称
            success: 是否成功
        """
        try:
            # 更新内存中的统计（用于实时显示）
            if endpoint in self.api_stats:
                self.api_stats[endpoint]['total'] += 1
                if success:
                    self.api_stats[endpoint]['success'] += 1
                else:
                    self.api_stats[endpoint]['errors'] += 1
            
            # 更新数据库中的统计（用于持久化）
            if self.db_manager:
                try:
                    method = self.stats_config.get(endpoint, {}).get('method', 'GET')
                    self.db_manager.update_api_call_statistics(endpoint, method, success)
                except Exception as e:
                    self.logger.error(f"更新数据库API统计失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"更新API统计失败: {e}")
    
    def get_success_rate(self, endpoint: str) -> float:
        """
        计算成功率
        
        Args:
            endpoint: API端点名称
            
        Returns:
            float: 成功率百分比
        """
        stats = self.api_stats.get(endpoint, {'total': 0, 'success': 0})
        if stats['total'] == 0:
            return 0.0
        return round((stats['success'] / stats['total']) * 100, 2)
    
    def get_enabled_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取启用的统计接口
        
        Returns:
            Dict[str, Dict[str, Any]]: 启用的统计配置
        """
        return {k: v for k, v in self.stats_config.items() if v['enabled']}
    
    def update_inference_timing_stats(self, timing_info: Dict[str, float]):
        """
        更新推理时间统计（排除预热推理）
        
        Args:
            timing_info: 时间信息字典
        """
        try:
            # 提取时间信息
            preprocess_time = timing_info.get('preprocess_time', 0)
            inference_time = timing_info.get('inference_time', 0)
            postprocess_time = timing_info.get('postprocess_time', 0)
            annotation_time = timing_info.get('image_annotation_time', 0)
            total_time = timing_info.get('total_time', 0)

            # 更新统计
            self.inference_timing_stats['total_inferences'] += 1
            self.inference_timing_stats['total_time_ms'] += total_time

            # 添加到时间列表（保留最近的记录）
            max_records = self.config.statistics.max_timing_records
            self.inference_timing_stats['preprocess_times'].append(preprocess_time)
            self.inference_timing_stats['inference_times'].append(inference_time)
            self.inference_timing_stats['postprocess_times'].append(postprocess_time)
            self.inference_timing_stats['annotation_times'].append(annotation_time)
            self.inference_timing_stats['total_times'].append(total_time)

            # 保持列表大小
            for key in ['preprocess_times', 'inference_times', 'postprocess_times', 'annotation_times', 'total_times']:
                if len(self.inference_timing_stats[key]) > max_records:
                    self.inference_timing_stats[key] = self.inference_timing_stats[key][-max_records:]

            # 更新最小/最大值
            self.inference_timing_stats['min_total_time'] = min(self.inference_timing_stats['min_total_time'], total_time)
            self.inference_timing_stats['max_total_time'] = max(self.inference_timing_stats['max_total_time'], total_time)
            self.inference_timing_stats['min_inference_time'] = min(self.inference_timing_stats['min_inference_time'], inference_time)
            self.inference_timing_stats['max_inference_time'] = max(self.inference_timing_stats['max_inference_time'], inference_time)

            # 计算平均值
            if self.inference_timing_stats['total_inferences'] > 0:
                self.inference_timing_stats['avg_total_time'] = round(
                    self.inference_timing_stats['total_time_ms'] / self.inference_timing_stats['total_inferences'], 2
                )
                self.inference_timing_stats['avg_inference_time'] = round(
                    sum(self.inference_timing_stats['inference_times']) / len(self.inference_timing_stats['inference_times']), 2
                )

        except Exception as e:
            self.logger.error(f"更新推理时间统计失败: {e}")
    
    def get_timing_statistics(self) -> Dict[str, Any]:
        """
        获取推理时间统计信息

        Returns:
            Dict[str, Any]: 时间统计信息
        """
        # 首先尝试从数据库获取统计
        db_stats = self._get_timing_stats_from_db()
        if db_stats and db_stats['total_inferences'] > 0:
            return db_stats

        # 如果数据库没有数据，使用内存统计
        if self.inference_timing_stats['total_inferences'] == 0:
            return {
                'total_inferences': 0,
                'message': '暂无推理数据（预热推理不计入统计）'
            }

        # 计算最近的统计信息
        recent_times = self.inference_timing_stats['total_times'][-10:] if len(self.inference_timing_stats['total_times']) >= 10 else self.inference_timing_stats['total_times']
        recent_inference_times = self.inference_timing_stats['inference_times'][-10:] if len(self.inference_timing_stats['inference_times']) >= 10 else self.inference_timing_stats['inference_times']

        stats = {
            'total_inferences': self.inference_timing_stats['total_inferences'],
            'overall': {
                'avg_total_time_ms': self.inference_timing_stats['avg_total_time'],
                'min_total_time_ms': round(self.inference_timing_stats['min_total_time'], 2) if self.inference_timing_stats['min_total_time'] != float('inf') else 0,
                'max_total_time_ms': round(self.inference_timing_stats['max_total_time'], 2),
                'avg_inference_time_ms': self.inference_timing_stats['avg_inference_time'],
                'min_inference_time_ms': round(self.inference_timing_stats['min_inference_time'], 2) if self.inference_timing_stats['min_inference_time'] != float('inf') else 0,
                'max_inference_time_ms': round(self.inference_timing_stats['max_inference_time'], 2)
            },
            'recent_10': {
                'avg_total_time_ms': round(sum(recent_times) / len(recent_times), 2) if recent_times else 0,
                'min_total_time_ms': round(min(recent_times), 2) if recent_times else 0,
                'max_total_time_ms': round(max(recent_times), 2) if recent_times else 0,
                'avg_inference_time_ms': round(sum(recent_inference_times) / len(recent_inference_times), 2) if recent_inference_times else 0
            }
        }

        return stats
    
    def get_api_statistics(self) -> Dict[str, Any]:
        """
        获取API调用统计信息
        
        Returns:
            Dict[str, Any]: API统计信息
        """
        try:
            # 从数据库获取总体统计
            total_stats = {'total_calls': 0, 'successful_calls': 0, 'failed_calls': 0}
            if self.db_manager:
                try:
                    total_stats = self.db_manager.get_total_api_calls()
                except Exception as e:
                    self.logger.error(f"获取数据库API统计失败: {e}")
            
            # 计算成功率
            total_calls = total_stats['total_calls']
            total_success = total_stats['successful_calls']
            overall_success_rate = round((total_success / total_calls * 100), 2) if total_calls > 0 else 0.0
            
            return {
                'total_calls': total_calls,
                'successful_calls': total_success,
                'failed_calls': total_stats['failed_calls'],
                'overall_success_rate': overall_success_rate,
                'memory_stats': self.api_stats,
                'enabled_endpoints': self.get_enabled_stats()
            }
            
        except Exception as e:
            self.logger.error(f"获取API统计失败: {e}")
            return {
                'total_calls': 0,
                'successful_calls': 0,
                'failed_calls': 0,
                'overall_success_rate': 0.0,
                'memory_stats': self.api_stats,
                'enabled_endpoints': self.get_enabled_stats()
            }
    
    def get_false_positive_statistics(self) -> Dict[str, Any]:
        """
        获取误报统计信息
        
        Returns:
            Dict[str, Any]: 误报统计信息
        """
        false_positive_stats = {
            'total_requests': 0,
            'total_false_positives': 0,
            'overall_false_positive_rate': 0.0
        }
        
        if self.db_manager:
            try:
                # 获取所有时间的综合统计
                all_stats = self.db_manager.get_combined_daily_statistics(days=365)
                if all_stats:
                    total_requests = sum(stat['request_count'] for stat in all_stats)
                    total_false_positives = sum(stat['false_positive_count'] for stat in all_stats)
                    
                    false_positive_stats = {
                        'total_requests': total_requests,
                        'total_false_positives': total_false_positives,
                        'overall_false_positive_rate': round((total_false_positives / total_requests * 100), 2) if total_requests > 0 else 0.0
                    }
            except Exception as e:
                self.logger.error(f"获取误报统计失败: {e}")
        
        return false_positive_stats
    
    def get_daily_statistics(self, days: int = 15) -> List[Dict[str, Any]]:
        """
        获取每日统计数据
        
        Args:
            days: 获取最近多少天的数据
            
        Returns:
            List[Dict[str, Any]]: 每日统计数据列表
        """
        if self.db_manager:
            try:
                return self.db_manager.get_combined_daily_statistics(days=days)
            except Exception as e:
                self.logger.error(f"获取每日统计数据失败: {e}")
        
        return []

    def _get_timing_stats_from_db(self) -> Dict[str, Any]:
        """
        从数据库获取推理时间统计

        Returns:
            Dict[str, Any]: 时间统计信息
        """
        if not self.db_manager:
            return None

        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # 获取总体时间统计
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_inferences,
                        AVG(total_time_ms) as avg_total_time_ms,
                        MIN(total_time_ms) as min_total_time_ms,
                        MAX(total_time_ms) as max_total_time_ms,
                        AVG(inference_time_ms) as avg_inference_time_ms,
                        MIN(inference_time_ms) as min_inference_time_ms,
                        MAX(inference_time_ms) as max_inference_time_ms,
                        AVG(preprocess_time_ms) as avg_preprocess_time_ms,
                        AVG(postprocess_time_ms) as avg_postprocess_time_ms
                    FROM inference_records
                    WHERE status = 'success'
                """)

                result = cursor.fetchone()
                if result and result[0] > 0:
                    return {
                        'total_inferences': result[0],
                        'overall': {
                            'avg_total_time_ms': round(result[1] or 0, 2),
                            'min_total_time_ms': round(result[2] or 0, 2),
                            'max_total_time_ms': round(result[3] or 0, 2),
                            'avg_inference_time_ms': round(result[4] or 0, 2),
                            'min_inference_time_ms': round(result[5] or 0, 2),
                            'max_inference_time_ms': round(result[6] or 0, 2),
                            'avg_preprocess_time_ms': round(result[7] or 0, 2),
                            'avg_postprocess_time_ms': round(result[8] or 0, 2)
                        },
                        'source': 'database'
                    }

                return None

        except Exception as e:
            self.logger.error(f"从数据库获取时间统计失败: {e}")
            return None
