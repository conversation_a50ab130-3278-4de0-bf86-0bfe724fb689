#!/usr/bin/env python3
"""
数据删除脚本
根据指定日期删除所有相关数据，包括推理记录、检测结果、图像信息、统计数据等
"""

import os
import sys
import sqlite3
import argparse
import shutil
from datetime import datetime, date
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from src.core.config import get_config
from src.utils.logging_config import get_logger


class DataDeleter:
    """数据删除器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化数据删除器
        
        Args:
            db_path: 数据库路径，如果为None则使用配置文件中的路径
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        if db_path:
            self.db_path = Path(db_path)
        else:
            self.db_path = Path(self.config.database.path)
        
        if not self.db_path.exists():
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
    
    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)
    
    def get_data_summary_by_date(self, target_date: str) -> Dict[str, Any]:
        """
        获取指定日期的数据摘要
        
        Args:
            target_date: 目标日期 (YYYY-MM-DD格式)
            
        Returns:
            Dict[str, Any]: 数据摘要
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                summary = {
                    'date': target_date,
                    'inference_records': 0,
                    'detection_results': 0,
                    'image_info': 0,
                    'false_positive_reports': 0,
                    'statistics_summary': 0,
                    'image_files': [],
                    'total_image_files': 0
                }
                
                # 获取推理记录数量
                cursor.execute("""
                    SELECT COUNT(*) FROM inference_records 
                    WHERE DATE(created_at) = ?
                """, (target_date,))
                summary['inference_records'] = cursor.fetchone()[0]
                
                # 获取检测结果数量
                cursor.execute("""
                    SELECT COUNT(*) FROM detection_results dr
                    JOIN inference_records ir ON dr.session_id = ir.session_id
                    WHERE DATE(ir.created_at) = ?
                """, (target_date,))
                summary['detection_results'] = cursor.fetchone()[0]
                
                # 获取图像信息数量
                cursor.execute("""
                    SELECT COUNT(*) FROM image_info ii
                    JOIN inference_records ir ON ii.session_id = ir.session_id
                    WHERE DATE(ir.created_at) = ?
                """, (target_date,))
                summary['image_info'] = cursor.fetchone()[0]
                
                # 获取误报记录数量
                cursor.execute("""
                    SELECT COUNT(*) FROM false_positive_reports 
                    WHERE DATE(created_at) = ?
                """, (target_date,))
                summary['false_positive_reports'] = cursor.fetchone()[0]
                
                # 获取统计摘要数量
                cursor.execute("""
                    SELECT COUNT(*) FROM statistics_summary 
                    WHERE date_key = ?
                """, (target_date,))
                summary['statistics_summary'] = cursor.fetchone()[0]
                
                # 获取图像文件路径
                cursor.execute("""
                    SELECT DISTINCT original_image_path, annotated_image_path 
                    FROM inference_records 
                    WHERE DATE(created_at) = ? 
                    AND (original_image_path IS NOT NULL OR annotated_image_path IS NOT NULL)
                """, (target_date,))
                
                image_paths = cursor.fetchall()
                for original_path, annotated_path in image_paths:
                    if original_path:
                        summary['image_files'].append(original_path)
                    if annotated_path:
                        summary['image_files'].append(annotated_path)
                
                summary['total_image_files'] = len(summary['image_files'])
                
                return summary
                
        except Exception as e:
            self.logger.error(f"获取数据摘要失败: {e}")
            raise
    
    def delete_data_by_date(self, target_date: str, delete_images: bool = True, 
                           dry_run: bool = False) -> Dict[str, Any]:
        """
        删除指定日期的所有数据
        
        Args:
            target_date: 目标日期 (YYYY-MM-DD格式)
            delete_images: 是否删除图像文件
            dry_run: 是否为试运行模式（不实际删除）
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            # 首先获取数据摘要
            summary = self.get_data_summary_by_date(target_date)
            
            if dry_run:
                self.logger.info(f"试运行模式 - 将要删除的数据摘要:")
                self._print_summary(summary)
                return {
                    'success': True,
                    'dry_run': True,
                    'summary': summary,
                    'message': '试运行完成，未实际删除数据'
                }
            
            # 确认删除
            if not self._confirm_deletion(summary):
                return {
                    'success': False,
                    'cancelled': True,
                    'message': '用户取消删除操作'
                }
            
            deleted_counts = {
                'inference_records': 0,
                'detection_results': 0,
                'image_info': 0,
                'false_positive_reports': 0,
                'statistics_summary': 0,
                'image_files': 0
            }
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 开始事务
                conn.execute("BEGIN TRANSACTION")
                
                try:
                    # 1. 删除检测结果（外键约束）
                    cursor.execute("""
                        DELETE FROM detection_results 
                        WHERE session_id IN (
                            SELECT session_id FROM inference_records 
                            WHERE DATE(created_at) = ?
                        )
                    """, (target_date,))
                    deleted_counts['detection_results'] = cursor.rowcount
                    
                    # 2. 删除图像信息
                    cursor.execute("""
                        DELETE FROM image_info 
                        WHERE session_id IN (
                            SELECT session_id FROM inference_records 
                            WHERE DATE(created_at) = ?
                        )
                    """, (target_date,))
                    deleted_counts['image_info'] = cursor.rowcount
                    
                    # 3. 删除误报记录
                    cursor.execute("""
                        DELETE FROM false_positive_reports 
                        WHERE DATE(created_at) = ?
                    """, (target_date,))
                    deleted_counts['false_positive_reports'] = cursor.rowcount
                    
                    # 4. 删除推理记录
                    cursor.execute("""
                        DELETE FROM inference_records 
                        WHERE DATE(created_at) = ?
                    """, (target_date,))
                    deleted_counts['inference_records'] = cursor.rowcount
                    
                    # 5. 删除统计摘要
                    cursor.execute("""
                        DELETE FROM statistics_summary 
                        WHERE date_key = ?
                    """, (target_date,))
                    deleted_counts['statistics_summary'] = cursor.rowcount
                    
                    # 提交事务
                    conn.commit()
                    
                    self.logger.info(f"数据库记录删除完成: {deleted_counts}")
                    
                except Exception as e:
                    # 回滚事务
                    conn.rollback()
                    raise e
            
            # 6. 删除图像文件
            if delete_images and summary['image_files']:
                deleted_files = self._delete_image_files(summary['image_files'])
                deleted_counts['image_files'] = deleted_files
            
            result = {
                'success': True,
                'date': target_date,
                'deleted_counts': deleted_counts,
                'summary': summary,
                'message': f'成功删除 {target_date} 的所有数据'
            }
            
            self.logger.info(f"数据删除完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"删除数据失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'删除 {target_date} 的数据时发生错误'
            }
    
    def _print_summary(self, summary: Dict[str, Any]):
        """打印数据摘要"""
        print(f"\n📅 日期: {summary['date']}")
        print(f"📊 推理记录: {summary['inference_records']} 条")
        print(f"🎯 检测结果: {summary['detection_results']} 条")
        print(f"🖼️  图像信息: {summary['image_info']} 条")
        print(f"❌ 误报记录: {summary['false_positive_reports']} 条")
        print(f"📈 统计摘要: {summary['statistics_summary']} 条")
        print(f"📁 图像文件: {summary['total_image_files']} 个")
        
        if summary['image_files']:
            print("\n图像文件列表:")
            for i, file_path in enumerate(summary['image_files'][:10], 1):
                print(f"  {i}. {file_path}")
            if len(summary['image_files']) > 10:
                print(f"  ... 还有 {len(summary['image_files']) - 10} 个文件")
    
    def _confirm_deletion(self, summary: Dict[str, Any]) -> bool:
        """确认删除操作"""
        print("\n" + "="*60)
        print("⚠️  警告: 即将删除以下数据，此操作不可逆!")
        print("="*60)
        
        self._print_summary(summary)
        
        print("\n" + "="*60)
        response = input("确认删除? 请输入 'YES' 来确认: ").strip()
        return response == 'YES'
    
    def _delete_image_files(self, image_files: List[str]) -> int:
        """
        删除图像文件
        
        Args:
            image_files: 图像文件路径列表
            
        Returns:
            int: 成功删除的文件数量
        """
        deleted_count = 0
        
        for file_path in image_files:
            try:
                if file_path and Path(file_path).exists():
                    Path(file_path).unlink()
                    deleted_count += 1
                    self.logger.debug(f"删除图像文件: {file_path}")
            except Exception as e:
                self.logger.warning(f"删除图像文件失败 {file_path}: {e}")
        
        return deleted_count


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="删除指定日期的所有数据")
    parser.add_argument("date", help="要删除的日期 (YYYY-MM-DD格式)")
    parser.add_argument("--db-path", help="数据库文件路径")
    parser.add_argument("--keep-images", action="store_true", 
                       help="保留图像文件，只删除数据库记录")
    parser.add_argument("--dry-run", action="store_true", 
                       help="试运行模式，只显示将要删除的数据，不实际删除")
    parser.add_argument("--summary-only", action="store_true",
                       help="只显示数据摘要，不执行删除")
    
    args = parser.parse_args()
    
    # 验证日期格式
    try:
        datetime.strptime(args.date, '%Y-%m-%d')
    except ValueError:
        print(f"❌ 错误: 日期格式不正确，请使用 YYYY-MM-DD 格式")
        sys.exit(1)
    
    try:
        # 创建数据删除器
        deleter = DataDeleter(args.db_path)
        
        if args.summary_only:
            # 只显示摘要
            summary = deleter.get_data_summary_by_date(args.date)
            print(f"\n📊 {args.date} 数据摘要:")
            deleter._print_summary(summary)
            return
        
        # 执行删除操作
        result = deleter.delete_data_by_date(
            target_date=args.date,
            delete_images=not args.keep_images,
            dry_run=args.dry_run
        )
        
        if result['success']:
            if result.get('dry_run'):
                print(f"\n✅ {result['message']}")
            elif result.get('cancelled'):
                print(f"\n❌ {result['message']}")
            else:
                print(f"\n✅ {result['message']}")
                print(f"删除统计: {result['deleted_counts']}")
        else:
            print(f"\n❌ {result['message']}")
            if 'error' in result:
                print(f"错误详情: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
