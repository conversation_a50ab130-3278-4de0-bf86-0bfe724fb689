# 数据管理脚本使用说明

## 1. 数据删除脚本 (delete_data_by_date.py)

这个脚本可以根据指定日期删除所有相关数据，包括推理记录、检测结果、图像信息、统计数据等。

### 基本用法

```bash
# 查看指定日期的数据摘要（不删除）
python scripts/delete_data_by_date.py 2025-08-05 --summary-only

# 试运行模式（显示将要删除的数据，但不实际删除）
python scripts/delete_data_by_date.py 2025-08-05 --dry-run

# 删除指定日期的所有数据（包括图像文件）
python scripts/delete_data_by_date.py 2025-08-05

# 只删除数据库记录，保留图像文件
python scripts/delete_data_by_date.py 2025-08-05 --keep-images

# 指定数据库文件路径
python scripts/delete_data_by_date.py 2025-08-05 --db-path /path/to/database.db
```

### 参数说明

- `date`: 要删除的日期，格式为 YYYY-MM-DD
- `--summary-only`: 只显示数据摘要，不执行删除
- `--dry-run`: 试运行模式，显示将要删除的数据但不实际删除
- `--keep-images`: 保留图像文件，只删除数据库记录
- `--db-path`: 指定数据库文件路径（可选）

### 安全特性

1. **确认机制**: 实际删除前需要输入 'YES' 确认
2. **试运行模式**: 可以先查看将要删除的数据
3. **事务保护**: 数据库操作使用事务，失败时自动回滚
4. **详细日志**: 记录所有操作和错误信息

### 删除的数据类型

- 推理记录 (inference_records)
- 检测结果 (detection_results)  
- 图像信息 (image_info)
- 误报记录 (false_positive_reports)
- 统计摘要 (statistics_summary)
- 图像文件（原始图像和标注图像）

### 示例输出

```bash
$ python scripts/delete_data_by_date.py 2025-08-05 --summary-only

📊 2025-08-05 数据摘要:

📅 日期: 2025-08-05
📊 推理记录: 4 条
🎯 检测结果: 0 条
🖼️  图像信息: 0 条
❌ 误报记录: 1 条
📈 统计摘要: 0 条
📁 图像文件: 4 个

图像文件列表:
  1. original/2025/08/05/20250805_185636_31ea3551_original.jpg
  2. original/2025/08/05/20250805_185728_dc44a617_original.jpg
  3. original/2025/08/05/20250805_185814_57615ed7_original.jpg
  4. original/2025/08/05/20250805_185814_967ad341_original.jpg
```

### 注意事项

⚠️ **重要警告**: 
- 删除操作不可逆，请务必在删除前备份重要数据
- 建议先使用 `--dry-run` 或 `--summary-only` 查看将要删除的数据
- 删除操作会影响统计数据的准确性

### 常见使用场景

1. **清理测试数据**: 删除测试期间产生的数据
2. **数据维护**: 定期清理过期数据
3. **错误数据清理**: 删除有问题的推理记录
4. **存储空间管理**: 清理占用空间较大的历史数据

### 错误处理

如果脚本执行失败，请检查：
1. 数据库文件是否存在且可访问
2. 是否有足够的权限删除文件
3. 数据库是否被其他进程占用
4. 日期格式是否正确 (YYYY-MM-DD)

### 相关命令

```bash
# 查看服务状态
./run.sh status

# 查看数据库统计
python data_management_tool.py --stats

# 备份数据库
cp yolo_inference.db yolo_inference_backup_$(date +%Y%m%d).db
```

## 2. 端口冲突管理脚本 (manage_port_conflict.py)

这个脚本帮助处理端口冲突问题，特别是5000端口被占用的情况。

### 基本用法

```bash
# 检查5000端口占用情况
python scripts/manage_port_conflict.py --check

# 检查其他端口
python scripts/manage_port_conflict.py --port 8000 --check

# 终止占用5000端口的进程
python scripts/manage_port_conflict.py --kill

# 强制终止（不询问确认）
python scripts/manage_port_conflict.py --kill --force

# 终止进程并启动开发环境服务 (8000端口)
python scripts/manage_port_conflict.py --kill --start-dev

# 终止进程并启动生产环境服务 (5000端口)
python scripts/manage_port_conflict.py --kill --start-prod
```

### 参数说明

- `--port`: 指定要检查的端口号 (默认: 5000)
- `--check`: 只检查端口占用情况，不执行其他操作
- `--kill`: 终止占用端口的进程
- `--force`: 强制终止，不询问确认
- `--start-dev`: 启动开发环境服务 (使用 config/default.json, 8000端口)
- `--start-prod`: 启动生产环境服务 (使用 config/production.json, 5000端口)

### 常见场景

#### macOS AirPlay Receiver 占用5000端口

```bash
# 检查是否是 AirPlay 占用
python scripts/manage_port_conflict.py --check

# 如果是 AirPlay，脚本会提示你:
# 1. 系统偏好设置 > 通用 > AirDrop与接力 > 取消勾选 'AirPlay接收器'
# 2. 或使用其他端口
```

#### 启动生产环境服务

```bash
# 方法1: 先检查端口，再手动处理
python scripts/manage_port_conflict.py --check
# 如果有冲突，手动处理后再启动
./run.sh start  # 使用 config/production.json

# 方法2: 自动处理冲突并启动
python scripts/manage_port_conflict.py --kill --start-prod
```

#### 本地开发

```bash
# 使用8000端口避免冲突
python scripts/manage_port_conflict.py --start-dev
# 或直接使用
./run.sh start  # 默认使用 config/default.json (8000端口)
```

### 配置文件说明

- `config/default.json`: 开发环境配置，使用8000端口
- `config/production.json`: 生产环境配置，使用5000端口

### 示例输出

```bash
$ python scripts/manage_port_conflict.py --check

🔍 检查端口 5000 占用情况...
❌ 端口 5000 被占用:
   进程: ControlCenter (PID: 12345)
   用户: ggec
   详情: ControlCenter 12345 ggec   10u  IPv4 0x... TCP *:commplex-main (LISTEN)

检测到 AirPlay Receiver 占用端口 5000
这是 macOS 系统服务，建议通过以下方式禁用:
1. 打开 系统偏好设置 > 通用 > AirDrop与接力
2. 取消勾选 'AirPlay接收器'
3. 或者使用不同的端口运行服务

端口 5000 冲突解决方案:
1. 终止占用端口的进程 (如果是你自己的进程)
2. 使用不同的端口运行服务
3. 如果是系统服务 (如 AirPlay)，在系统设置中禁用
```
