#!/usr/bin/env python3
"""
端口冲突管理脚本
帮助处理5000端口被占用的问题
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def get_process_using_port(port: int):
    """获取占用指定端口的进程信息"""
    try:
        result = subprocess.run(['lsof', '-i', f':{port}'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # 跳过标题行
                process_line = lines[1]
                parts = process_line.split()
                if len(parts) >= 2:
                    return {
                        'command': parts[0],
                        'pid': parts[1],
                        'user': parts[2] if len(parts) > 2 else 'unknown',
                        'full_line': process_line
                    }
        return None
    except Exception as e:
        print(f"检查端口时出错: {e}")
        return None


def kill_process_on_port(port: int, force: bool = False):
    """杀死占用指定端口的进程"""
    process_info = get_process_using_port(port)
    if not process_info:
        print(f"端口 {port} 没有被占用")
        return True
    
    pid = process_info['pid']
    command = process_info['command']
    
    print(f"发现进程占用端口 {port}:")
    print(f"  PID: {pid}")
    print(f"  命令: {command}")
    print(f"  详情: {process_info['full_line']}")
    
    if not force:
        response = input(f"确认要终止进程 {pid} ({command})? [y/N]: ").strip().lower()
        if response not in ['y', 'yes']:
            print("操作已取消")
            return False
    
    try:
        # 先尝试优雅终止
        subprocess.run(['kill', pid], check=True)
        print(f"已发送终止信号给进程 {pid}")
        
        # 等待一下检查是否还在运行
        import time
        time.sleep(2)
        
        if get_process_using_port(port):
            print("进程仍在运行，尝试强制终止...")
            subprocess.run(['kill', '-9', pid], check=True)
            print(f"已强制终止进程 {pid}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"终止进程失败: {e}")
        return False


def check_airplay_receiver():
    """检查是否是AirPlay Receiver占用5000端口"""
    process_info = get_process_using_port(5000)
    if process_info and 'ControlCenter' in process_info['command']:
        print("检测到 AirPlay Receiver 占用端口 5000")
        print("这是 macOS 系统服务，建议通过以下方式禁用:")
        print("1. 打开 系统偏好设置 > 通用 > AirDrop与接力")
        print("2. 取消勾选 'AirPlay接收器'")
        print("3. 或者使用不同的端口运行服务")
        return True
    return False


def suggest_solutions(port: int):
    """建议解决方案"""
    print(f"\n端口 {port} 冲突解决方案:")
    print("1. 终止占用端口的进程 (如果是你自己的进程)")
    print("2. 使用不同的端口运行服务")
    print("3. 如果是系统服务 (如 AirPlay)，在系统设置中禁用")
    
    if port == 5000:
        print("\n对于 5000 端口特别说明:")
        print("- macOS Monterey+ 默认启用 AirPlay Receiver 占用 5000 端口")
        print("- 可以使用 8000 端口作为替代 (已在 config/default.json 中配置)")
        print("- 生产环境可以使用 config/production.json (5000端口)")


def start_service_with_config(config_file: str):
    """使用指定配置文件启动服务"""
    script_dir = Path(__file__).parent.parent
    run_script = script_dir / "run.sh"
    
    if not run_script.exists():
        print(f"启动脚本不存在: {run_script}")
        return False
    
    try:
        # 修改run.sh中的配置文件路径
        cmd = f'cd "{script_dir}" && python main.py start --config "{config_file}"'
        print(f"启动命令: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("服务启动成功!")
            print(result.stdout)
            return True
        else:
            print("服务启动失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"启动服务时出错: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="端口冲突管理工具")
    parser.add_argument("--port", type=int, default=5000, help="要检查的端口号")
    parser.add_argument("--check", action="store_true", help="只检查端口占用情况")
    parser.add_argument("--kill", action="store_true", help="终止占用端口的进程")
    parser.add_argument("--force", action="store_true", help="强制终止，不询问确认")
    parser.add_argument("--start-dev", action="store_true", help="启动开发环境服务 (8000端口)")
    parser.add_argument("--start-prod", action="store_true", help="启动生产环境服务 (5000端口)")
    
    args = parser.parse_args()
    
    print(f"🔍 检查端口 {args.port} 占用情况...")
    
    process_info = get_process_using_port(args.port)
    
    if not process_info:
        print(f"✅ 端口 {args.port} 未被占用")
        
        if args.start_dev:
            print("启动开发环境服务...")
            start_service_with_config("config/default.json")
        elif args.start_prod:
            print("启动生产环境服务...")
            start_service_with_config("config/production.json")
            
        return
    
    print(f"❌ 端口 {args.port} 被占用:")
    print(f"   进程: {process_info['command']} (PID: {process_info['pid']})")
    print(f"   用户: {process_info['user']}")
    print(f"   详情: {process_info['full_line']}")
    
    if args.port == 5000:
        check_airplay_receiver()
    
    if args.check:
        suggest_solutions(args.port)
        return
    
    if args.kill:
        success = kill_process_on_port(args.port, args.force)
        if success:
            print(f"✅ 端口 {args.port} 已释放")
            
            if args.start_dev:
                print("启动开发环境服务...")
                start_service_with_config("config/default.json")
            elif args.start_prod:
                print("启动生产环境服务...")
                start_service_with_config("config/production.json")
        else:
            print(f"❌ 无法释放端口 {args.port}")
    else:
        suggest_solutions(args.port)


if __name__ == "__main__":
    main()
